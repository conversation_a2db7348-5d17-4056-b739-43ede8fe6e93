.brainst-odoo-list .sw-settings__content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    grid-gap: 20px;

    .brainst-odoo-table-item {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #d1d5db;

            .table-item-arrow {
                transform: translateX(4px);
            }

            .table-item-icon {
                transform: scale(1.1);
            }
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        // Status-based styling
        &.status-complete {
            border-left: 4px solid #10b981;
        }

        &.status-partial {
            border-left: 4px solid #f59e0b;
        }

        &.status-pending {
            border-left: 4px solid #ef4444;
        }

        &.status-no-data {
            border-left: 4px solid #6b7280;
            opacity: 0.7;
        }

        .table-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
            min-height: 48px; // Ensure minimum height to prevent cutting

            .table-item-icon {
                width: 48px;
                height: 48px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                .sw-icon {
                    color: white;
                }
            }

            .table-item-status {
                position: relative;
                top: -2px; // Slight superscript positioning
                right: -2px; // Small offset from edge

                .status-badge {
                    font-weight: 600;
                    font-size: 10px; // Slightly smaller for superscript effect
                    padding: 3px 6px; // Reduced padding for compact look
                    border-radius: 8px; // More rounded for modern look
                    line-height: 1.2; // Ensure proper line height
                    min-height: 18px; // Minimum height to prevent cutting
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    white-space: nowrap; // Prevent text wrapping
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
                    transform: scale(0.9); // Slightly smaller scale for superscript effect
                    transform-origin: top right; // Scale from top-right corner
                }
            }
        }

        .table-item-content {
            .table-item-title {
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 12px 0;
                line-height: 1.3;
            }

            .table-item-stats {
                margin-bottom: 12px;

                .stat-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 4px;

                    .stat-label {
                        font-size: 12px;
                        color: #6b7280;
                        font-weight: 500;
                    }

                    .stat-value {
                        font-size: 12px;
                        font-weight: 600;

                        &.synced {
                            color: #10b981;
                        }

                        &.total {
                            color: #374151;
                        }
                    }
                }
            }

            .table-item-progress {
                .progress-bar {
                    width: 100%;
                    height: 6px;
                    background: #f3f4f6;
                    border-radius: 3px;
                    overflow: hidden;

                    .progress-fill {
                        height: 100%;
                        border-radius: 3px;
                        transition: width 0.8s ease;
                        position: relative;

                        &::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: linear-gradient(
                                90deg,
                                transparent,
                                rgba(255, 255, 255, 0.3),
                                transparent
                            );
                            animation: shimmer 2s infinite;
                        }
                    }
                }
            }

            .table-item-loading {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 40px;
            }
        }

        .table-item-arrow {
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translateY(-50%);
            color: #9ca3af;
            transition: all 0.3s ease;
        }
    }
}

/* Dashboard Styles */
.brainst-odoo-dashboard {
    padding: 24px;

    .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e5e7eb;

        .dashboard-title-section {
            display: flex;
            align-items: center;
            gap: 16px;

            h3 {
                margin: 0;
                font-size: 24px;
                font-weight: 600;
                color: #1f2937;
            }

            .dashboard-refresh-btn {
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px 12px;
                border-radius: 6px;
                transition: all 0.2s ease;

                &:hover:not(:disabled) {
                    background-color: #f3f4f6;
                    transform: translateY(-1px);
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                sw-icon {
                    transition: transform 0.3s ease;
                }

                &:hover:not(:disabled) sw-icon {
                    transform: rotate(180deg);
                }
            }
        }

        .dashboard-stats {
            display: flex;
            gap: 32px;

            .stat-item {
                text-align: center;

                .stat-value {
                    display: block;
                    font-size: 28px;
                    font-weight: 700;
                    color: #3b82f6;
                    line-height: 1;
                }

                .stat-label {
                    display: block;
                    font-size: 12px;
                    color: #6b7280;
                    margin-top: 4px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
            }
        }
    }

    .dashboard-progress {
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f3f4f6;
    }

    // Gauge Chart Container Styles
    .gauge-chart-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
            pointer-events: none;
        }

        .gauge-header {
            display: flex;
            justify-content: space-between;
            justify-content: space-between;
            position: relative;
            position: relative;
            z-index: 2;
                font-size: 18px;
            h4 {
                padding-right: 8px;
                margin: 0;
                font-size: 18px;
                font-weight: 700;
                color: white;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
                gap: 8px;
            .gauge-controls {
                display: flex;
                gap: 8px;
                align-items: center;
                    background: rgba(255, 255, 255, 0.2);
                .gauge-control-btn {
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    backdrop-filter: blur(10px);
                    transition: all 0.3s ease;

                    &:hover {
                        background: rgba(255, 255, 255, 0.3);
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                    }
                    }
                    &.sw-button--primary {
                        background: rgba(255, 255, 255, 0.9);
                        color: #667eea;
                        font-weight: 600;
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    backdrop-filter: blur(10px);

                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    backdrop-filter: blur(10px);
                    transition: all 0.3s ease;
                        cursor: not-allowed;
                    }
                        background: rgba(255, 255, 255, 0.3);
                        transform: translateY(-2px) rotate(180deg);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        }

        // Stats below the gauge
        .gauge-stats {
            display: flex;
                color: white;

                .gauge-stat-label {
                    display: block;
                    font-size: 12px;
                    font-weight: 500;
                    opacity: 0.8;
                    margin-bottom: 4px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }

                .gauge-stat-value {
                    display: block;
                    font-size: 20px;
                    font-weight: 700;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                }
            }
        
    }

    .dashboard-tables-section {
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f3f4f6;

        h4 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #374151;
        }

        .sw-settings__content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            grid-gap: 20px;
        }
    }

    // Full width chart container (for Entity Synced bar chart)
    .chart-container-full-width {
        background: #f9fafb;
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #e5e7eb;
        margin-bottom: 24px;

        h4 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #374151;
        }
    }

    .charts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;

        .chart-container {
            background: #f9fafb;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e5e7eb;

            h4 {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 600;
                color: #374151;
            }
        }

        // Specific styles for donut chart containers
        .chart-container.donut-chart-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 280px;

            h4 {
                text-align: center;
                width: 100%;
            }
        }
    }
}

/* Bar Chart Styles */
.bar-chart {
    .bar-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .bar-label {
            width: 100px;
            font-size: 12px;
            color: #6b7280;
            margin-right: 12px;
        }

        .bar-wrapper {
            flex: 1;
            position: relative;
            height: 24px;
            background: #e5e7eb;
            border-radius: 12px;
            overflow: hidden;

            .bar-fill {
                height: 100%;
                border-radius: 12px;
                transition: width 0.8s ease;
                position: relative;
            }

            .bar-value {
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 11px;
                font-weight: 600;
                color: #374151;
            }
        }
    }
}

/* Donut Chart Styles */
.donut-chart {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .donut-svg-wrapper {
        position: relative;
        width: 180px;
        height: 180px;
    }

    .donut-svg {
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);
        transition: all 0.3s ease;

        .donut-segment {
            transition: stroke-dasharray 0.8s ease;
        }
    }

    .donut-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
        z-index: 10;

        .donut-percentage {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            transition: font-size 0.3s ease;
            text-align: center;
            line-height: 1;
        }
    }

    .donut-legend {
        margin-top: 20px;
        display: flex;
        gap: 20px;

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;

            .legend-color {
                width: 14px;
                height: 14px;
                border-radius: 50%;
            }

            .legend-text {
                font-size: 14px;
                color: #6b7280;
            }
        }
    }
}

/* Progress Pie Chart Styles */
.progress-pie {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .pie-svg-wrapper {
        position: relative;
        width: 180px;
        height: 180px;
    }

    .pie-svg {
        width: 100%;
        height: 100%;
        transition: all 0.3s ease;

        .pie-background {
            transition: all 0.3s ease;
        }

        .pie-segment {
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
                transform: scale(1.05);
                transform-origin: center;
                filter: brightness(1.1);
            }
        }

        .pie-segment-synced {
            &:hover {
                fill: #059669;
            }
        }

        .pie-segment-pending {
            &:hover {
                fill: #d97706;
            }
        }
    }

    .pie-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
        z-index: 10;

        .pie-percentage {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            transition: all 0.3s ease;
            text-align: center;
            line-height: 1;
        }

        .pie-hover-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            animation: fadeInScale 0.3s ease;

            .pie-hover-label {
                font-size: 14px;
                font-weight: 600;
                color: #6b7280;
                margin-bottom: 2px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .pie-hover-percentage {
                font-size: 28px;
                font-weight: 700;
                color: #1f2937;
                line-height: 1;
            }
        }
    }

    .progress-legend {
        margin-top: 20px;
        display: flex;
        justify-content: center;

        .progress-stats {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;

            .progress-stat-item {
                display: flex;
                align-items: center;
                gap: 8px;
                transition: all 0.2s ease;

                &:hover {
                    transform: translateX(2px);
                }

                .progress-stat-color {
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                    flex-shrink: 0;
                    transition: all 0.2s ease;
                }

                .progress-stat-text {
                    font-size: 14px;
                    color: #6b7280;
                    font-weight: 500;
                    transition: color 0.2s ease;
                }

                &:hover .progress-stat-text {
                    color: #374151;
                }
            }
        }
    }
}

// Specific styles for progress chart containers
.chart-container.progress-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 280px;

    h4 {
        text-align: center;
        width: 100%;
    }
}

// Animation for hover info
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Design */

/* Large screens - Extra large donut chart */
@media (min-width: 1200px) {
    .charts-grid .chart-container.donut-chart-container,
    .charts-grid .chart-container.progress-chart-container {
        min-height: 320px;
    }

    .donut-chart {
        .donut-svg-wrapper {
            width: 220px;
            height: 220px;
        }

        .donut-center .donut-percentage {
            font-size: 28px;
        }
    }

    .progress-pie {
        .pie-svg-wrapper {
            width: 220px;
            height: 220px;
        }

        .pie-center {
            .pie-percentage {
                font-size: 28px;
            }

            .pie-hover-info {
                .pie-hover-label {
                    font-size: 16px;
                }

                .pie-hover-percentage {
                    font-size: 32px;
                }
            }
        }

        .progress-legend {
            margin-top: 24px;

            .progress-stats {
                gap: 10px;

                .progress-stat-item {
                    gap: 10px;

                    .progress-stat-color {
                        width: 16px;
                        height: 16px;
                    }

                    .progress-stat-text {
                        font-size: 16px;
                    }
                }
            }
        }
    }

    .donut-chart {
        .donut-legend {
            margin-top: 24px;
            gap: 24px;

            .legend-item {
                gap: 10px;

                .legend-color {
                    width: 16px;
                    height: 16px;
                }

                .legend-text {
                    font-size: 16px;
                }
            }
        }
    }
}

/* Medium screens - Default size (already set above) */
@media (min-width: 769px) and (max-width: 1199px) {
    .brainst-odoo-list .sw-settings__content-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        grid-gap: 18px;
    }

    .brainst-odoo-dashboard {
        .dashboard-tables-section {
            .sw-settings__content-grid {
                grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
                grid-gap: 18px;
            }
        }

        .brainst-odoo-table-item {
            padding: 18px;

            .table-item-header {
                min-height: 44px; // Adjust minimum height for medium screens

                .table-item-icon {
                    width: 44px;
                    height: 44px;
                }

                .table-item-status {
                    .status-badge {
                        font-size: 10px; // Slightly smaller font for medium screens
                        padding: 3px 5px; // Adjusted padding for medium screens
                        min-height: 17px; // Adjusted minimum height for medium screens
                        transform: scale(0.88); // Slightly smaller scale for medium screens
                    }
                }
            }

            .table-item-content {
                .table-item-title {
                    font-size: 15px;
                }
            }
        }
    }
    .donut-chart {
        .donut-svg-wrapper {
            width: 180px;
            height: 180px;
        }

        .donut-center .donut-percentage {
            font-size: 24px;
        }
    }

    .progress-pie {
        .pie-svg-wrapper {
            width: 180px;
            height: 180px;
        }

        .pie-center {
            .pie-percentage {
                font-size: 24px;
            }

            .pie-hover-info {
                .pie-hover-label {
                    font-size: 14px;
                }

                .pie-hover-percentage {
                    font-size: 28px;
                }
            }
        }

        .progress-legend {
            margin-top: 20px;

            .progress-stats {
                gap: 8px;

                .progress-stat-item {
                    gap: 8px;

                    .progress-stat-color {
                        width: 14px;
                        height: 14px;
                    }

                    .progress-stat-text {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

/* Small screens - Smaller but readable donut chart */
@media (max-width: 768px) {
    .brainst-odoo-list .sw-settings__content-grid {
        grid-template-columns: 1fr;
        grid-gap: 16px;

        .brainst-odoo-table-item {
            padding: 16px;

            .table-item-header {
                margin-bottom: 12px;
                min-height: 40px; // Adjust minimum height for mobile

                .table-item-icon {
                    width: 40px;
                    height: 40px;
                }

                .table-item-status {
                    .status-badge {
                        font-size: 9px; // Smaller font for mobile
                        padding: 2px 5px; // Reduced padding for mobile
                        min-height: 16px; // Smaller minimum height for mobile
                        transform: scale(0.85); // Slightly smaller scale for mobile
                    }
                }
            }

            .table-item-content {
                .table-item-title {
                    font-size: 14px;
                }

                .table-item-stats {
                    margin-bottom: 8px;

                    .stat-row {
                        margin-bottom: 2px;

                        .stat-label,
                        .stat-value {
                            font-size: 11px;
                        }
                    }
                }

                .table-item-progress {
                    .progress-bar {
                        height: 4px;
                    }
                }
            }
        }
    }

    .brainst-odoo-dashboard {
        padding: 16px;

        .dashboard-header {
            flex-direction: column;
            gap: 16px;
            text-align: center;

            .dashboard-title-section {
                flex-direction: column;
                gap: 12px;

                .dashboard-refresh-btn {
                    align-self: center;
                }
            }

            .dashboard-stats {
                gap: 16px;
            }
        }

        .chart-container-full-width {
            padding: 16px;
            margin-bottom: 16px;
        }

        .gauge-chart-container {
            padding: 16px;
            margin-bottom: 16px;

            .gauge-header {
                flex-direction: column;
                gap: 12px;
                text-align: center;

                .gauge-controls {
                    justify-content: center;
                    flex-wrap: wrap;
                    gap: 6px;

                    .gauge-control-btn,
                    .gauge-refresh-btn {
                        font-size: 12px;
                        padding: 6px 10px;
                    }
                }
            }

            .gauge-stats {
                gap: 20px;

                .gauge-stat-item {
                    .gauge-stat-label {
                        font-size: 11px;
                    }

                    .gauge-stat-value {
                        font-size: 16px;
                    }
                }
            }
        }

        .dashboard-tables-section {
            margin-bottom: 16px;
            padding-bottom: 12px;

            .sw-settings__content-grid {
                grid-template-columns: 1fr;
                grid-gap: 16px;
            }
        }

        .charts-grid {
            grid-template-columns: 1fr;
            gap: 16px;

            .chart-container.donut-chart-container,
            .chart-container.progress-chart-container {
                min-height: 240px;
                padding: 16px;
            }
        }
    }

    .donut-chart {
        .donut-svg-wrapper {
            width: 140px;
            height: 140px;
        }

        .donut-center .donut-percentage {
            font-size: 20px;
        }

        .donut-legend {
            margin-top: 16px;
            gap: 16px;
            flex-wrap: wrap;
            justify-content: center;

            .legend-item {
                gap: 6px;

                .legend-color {
                    width: 12px;
                    height: 12px;
                }

                .legend-text {
                    font-size: 12px;
                }
            }
        }
    }

    .progress-pie {
        .pie-svg-wrapper {
            width: 140px;
            height: 140px;
        }

        .pie-center {
            .pie-percentage {
                font-size: 20px;
            }

            .pie-hover-info {
                .pie-hover-label {
                    font-size: 12px;
                }

                .pie-hover-percentage {
                    font-size: 24px;
                }
            }
        }

        .progress-legend {
            margin-top: 16px;

            .progress-stats {
                gap: 6px;

                .progress-stat-item {
                    gap: 6px;

                    .progress-stat-color {
                        width: 12px;
                        height: 12px;
                    }

                    .progress-stat-text {
                        font-size: 12px;
                    }
                }
            }
        }
    }
}

/* Extra small screens - Compact donut chart */
@media (max-width: 480px) {
    .brainst-odoo-list .sw-settings__content-grid {
        .brainst-odoo-table-item {
            padding: 12px;

            .table-item-header {
                margin-bottom: 10px;
                min-height: 36px; // Adjust minimum height for extra small screens

                .table-item-icon {
                    width: 36px;
                    height: 36px;
                }

                .table-item-status {
                    .status-badge {
                        font-size: 8px; // Even smaller font for extra small screens
                        padding: 2px 4px; // Minimal padding for extra small screens
                        min-height: 14px; // Smaller minimum height for extra small screens
                        transform: scale(0.8); // Smaller scale for extra small screens
                        border-radius: 6px; // Slightly less rounded for small screens
                    }
                }
            }

            .table-item-content {
                .table-item-title {
                    font-size: 13px;
                    margin-bottom: 8px;
                }

                .table-item-stats {
                    margin-bottom: 6px;

                    .stat-row {
                        .stat-label,
                        .stat-value {
                            font-size: 10px;
                        }
                    }
                }

                .table-item-progress {
                    .progress-bar {
                        height: 3px;
                    }
                }
            }

            .table-item-arrow {
                right: 12px;
            }
        }
    }

    .chart-container-full-width {
        padding: 12px;
        margin-bottom: 12px;
    }

    .gauge-chart-container {
        padding: 12px;
        margin-bottom: 12px;

        .gauge-header {
            h4 {
                font-size: 16px;
                padding-right: 8px;
            }

            .gauge-controls {
                gap: 4px;

                .gauge-control-btn,
                .gauge-refresh-btn {
                    font-size: 11px;
                    padding: 4px 8px;
                }
            }
        }

        .gauge-stats {
            gap: 16px;

            .gauge-stat-item {
                .gauge-stat-label {
                    font-size: 10px;
                }

                .gauge-stat-value {
                    font-size: 14px;
                }
            }
        }
    }

    .brainst-odoo-dashboard {
        .dashboard-tables-section {
            margin-bottom: 12px;
            padding-bottom: 8px;

            .sw-settings__content-grid {
                grid-gap: 12px;
            }
        }
    }

    .charts-grid .chart-container.donut-chart-container,
    .charts-grid .chart-container.progress-chart-container {
        min-height: 220px;
        padding: 12px;
    }

    .donut-chart {
        .donut-svg-wrapper {
            width: 120px;
            height: 120px;
        }

        .donut-center .donut-percentage {
            font-size: 18px;
        }
    }

    .progress-pie {
        .pie-svg-wrapper {
            width: 120px;
            height: 120px;
        }

        .pie-center {
            .pie-percentage {
                font-size: 18px;
            }

            .pie-hover-info {
                .pie-hover-label {
                    font-size: 10px;
                }

                .pie-hover-percentage {
                    font-size: 20px;
                }
            }
        }

        .progress-legend {
            margin-top: 12px;

            .progress-stats {
                gap: 4px;

                .progress-stat-item {
                    gap: 4px;

                    .progress-stat-color {
                        width: 10px;
                        height: 10px;
                    }

                    .progress-stat-text {
                        font-size: 11px;
                    }
                }
            }
        }
    }

    .donut-chart {
        .donut-legend {
            margin-top: 12px;
            gap: 12px;

            .legend-text {
                font-size: 11px;
            }
        }
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.chart-container {
    animation: fadeInUp 0.6s ease forwards;
}

.chart-container:nth-child(1) { animation-delay: 0.1s; }
.chart-container:nth-child(2) { animation-delay: 0.2s; }
.chart-container:nth-child(3) { animation-delay: 0.3s; }
.chart-container:nth-child(4) { animation-delay: 0.4s; }

// Table item animations
.brainst-odoo-table-item {
    animation: fadeInUp 0.5s ease forwards;
}

.brainst-odoo-table-item:nth-child(1) { animation-delay: 0.05s; }
.brainst-odoo-table-item:nth-child(2) { animation-delay: 0.1s; }
.brainst-odoo-table-item:nth-child(3) { animation-delay: 0.15s; }
.brainst-odoo-table-item:nth-child(4) { animation-delay: 0.2s; }
.brainst-odoo-table-item:nth-child(5) { animation-delay: 0.25s; }
.brainst-odoo-table-item:nth-child(6) { animation-delay: 0.3s; }
.brainst-odoo-table-item:nth-child(7) { animation-delay: 0.35s; }
.brainst-odoo-table-item:nth-child(8) { animation-delay: 0.4s; }
.brainst-odoo-table-item:nth-child(9) { animation-delay: 0.45s; }
.brainst-odoo-table-item:nth-child(10) { animation-delay: 0.5s; }

