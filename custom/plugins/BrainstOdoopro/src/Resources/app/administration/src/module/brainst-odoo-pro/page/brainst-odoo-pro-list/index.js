import template from './brainst-odoo-pro-list.html.twig';

import('./index.scss')

Shopware.Component.register('brainst-odoo-pro-list', {
    template,

    inject: ['brainstOdooProApiService'],

    data() {
        return {
            syncedCount: 0,
            totalRecords: 0,
            entityCounts: {},
            entitySynced: {},
            syncProgress: {},
            isLoading: true,
            hoverInfo: {
                show: false,
                label: '',
                percentage: 0
            },
            // Gauge chart data
            syncRate: {
                recordsPerHour: 0,
                recordsPerMinute: 0,
                displayRecordsPerHour: '0',
                displayRecordsPerMinute: '0',
                timestamp: null
            },
            selectedTimePeriod: 'hour', // 'hour' or 'minute'
            odooTables: [
                {
                    title: this.$tc('brainst-odoo-pro.list.sales-channel'),
                    path: "brainst.odoo.pro.sales-channel",
                    icon: "regular-storefront",
                    entityKey: "sales_channel",
                    color: "#3b82f6"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.category'),
                    path: "brainst.odoo.pro.category",
                    icon: "regular-folder",
                    entityKey: "category",
                    color: "#8b5cf6"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.customer'),
                    path: "brainst.odoo.pro.customer",
                    icon: "regular-user",
                    entityKey: "customer",
                    color: "#ef4444"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.customer-address'),
                    path: "brainst.odoo.pro.customer-address",
                    icon: "regular-map",
                    entityKey: "customer_address",
                    color: "#ec4899"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.property'),
                    path: "brainst.odoo.pro.property",
                    icon: "regular-cog",
                    entityKey: "attribute",
                    color: "#f59e0b"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.attribute-value'),
                    path: "brainst.odoo.pro.attribute-value",
                    icon: "regular-tag",
                    entityKey: "attribute_value",
                    color: "#f97316"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.product'),
                    path: "brainst.odoo.pro.product",
                    icon: "regular-products",
                    entityKey: "product",
                    color: "#10b981"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.order'),
                    path: "brainst.odoo.pro.order",
                    icon: "regular-shopping-cart",
                    entityKey: "order",
                    color: "#84cc16"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.delivery'),
                    path: "brainst.odoo.pro.delivery",
                    icon: "regular-truck",
                    entityKey: "delivery",
                    color: "#14b8a6"
                },
                {
                    title: this.$tc('brainst-odoo-pro.list.transaction'),
                    path: "brainst.odoo.pro.transaction",
                    icon: "regular-credit-card",
                    entityKey: "transaction",
                    color: "#a855f7"
                }
            ],
            timeoutId: null
        };
    },

    metaInfo() {
        return {
            title: this.$createTitle()
        };
    },

    created() {
        this.getAllData();
    },

    computed: {
        chartData() {
            if (!this.entityCounts || Object.keys(this.entityCounts).length === 0) {
                return [];
            }
            return Object.entries(this.entityCounts).map(([entity, count]) => ({
                name: this.getEntityDisplayName(entity),
                value: count || 0,
                synced: this.entitySynced[entity] || 0,
                color: this.getEntityColor(entity)
            }));
        },

        syncStatusData() {
            if (this.syncProgress && this.syncProgress.synced !== undefined) {
                return [
                    {
                        name: this.$tc('brainst-odoo-pro.dashboard.synced'),
                        value: this.syncProgress.synced,
                        color: '#10b981'
                    },
                    {
                        name: this.$tc('brainst-odoo-pro.dashboard.pending'),
                        value: this.syncProgress.pending,
                        color: '#f59e0b'
                    }
                ];
            }

            // Fallback calculation
            const total = Object.values(this.entityCounts).reduce((sum, count) => sum + count, 0);
            const synced = this.syncedCount;
            const pending = Math.max(0, total - synced);

            return [
                {name: this.$tc('brainst-odoo-pro.dashboard.synced'), value: synced, color: '#10b981'},
                {name: this.$tc('brainst-odoo-pro.dashboard.pending'), value: pending, color: '#f59e0b'}
            ];
        },

        // Gauge chart computed properties
        currentSyncRate() {
            if (this.selectedTimePeriod === 'hour') {
                return {
                    value: this.syncRate.recordsPerHour,
                    displayValue: this.syncRate.displayRecordsPerHour,
                    max: 999,
                    label: this.$tc('brainst-odoo-pro.dashboard.gauge.recordsPerHour'),
                    unit: this.$tc('brainst-odoo-pro.dashboard.gauge.hour')
                };
            } else {
                return {
                    value: this.syncRate.recordsPerMinute,
                    displayValue: this.syncRate.displayRecordsPerMinute,
                    max: 99,
                    label: this.$tc('brainst-odoo-pro.dashboard.gauge.recordsPerMinute'),
                    unit: this.$tc('brainst-odoo-pro.dashboard.gauge.minute')
                };
            }
        }
    },

    methods: {
        getAllData() {
            this.isLoading = true;

            // Fetch both record counts and sync rate data
            Promise.all([
                this.brainstOdooProApiService.recordCount(),
                this.brainstOdooProApiService.getSyncRate()
            ])
                .then(([recordResponse, syncRateResponse]) => {
                    // Handle record count response
                    if (recordResponse.isValid) {
                        // Update synced count for progress bar
                        this.syncedCount = recordResponse.syncedRecords || 0;
                        this.totalRecords = recordResponse.totalRecords || 0;

                        // Update entity counts for graphs
                        this.entityCounts = recordResponse.detailedCounts;
                        this.entitySynced = recordResponse.syncedCounts;

                        // Update sync progress data
                        this.syncProgress = {
                            synced: recordResponse.syncedRecords || 0,
                            pending: Math.max(0, (recordResponse.totalRecords || 0) - (recordResponse.syncedRecords || 0)),
                            total: recordResponse.totalRecords || 0,
                            percentage: (recordResponse.totalRecords || 0) > 0 ?
                                Math.round(((recordResponse.syncedRecords || 0) / (recordResponse.totalRecords || 0)) * 100) : 0
                        };
                    }

                    // Handle sync rate response
                    if (syncRateResponse.isValid) {
                        this.syncRate = {
                            recordsPerHour: syncRateResponse.recordsPerHour || 0,
                            recordsPerMinute: syncRateResponse.recordsPerMinute || 0,
                            displayRecordsPerHour: syncRateResponse.displayRecordsPerHour || '0',
                            displayRecordsPerMinute: syncRateResponse.displayRecordsPerMinute || '0',
                            timestamp: syncRateResponse.timestamp
                        };
                    }
                })
                .catch((error) => {
                    console.error('Failed to fetch data:', error);
                    // Fallback to empty data
                    this.syncedCount = 0;
                    this.entityCounts = {};
                    this.entitySynced = {};
                    this.syncProgress = {synced: 0, pending: 0, total: 0, percentage: 0};
                    this.syncRate = {
                        recordsPerHour: 0,
                        recordsPerMinute: 0,
                        displayRecordsPerHour: '0',
                        displayRecordsPerMinute: '0',
                        timestamp: null
                    };
                })
                .finally(() => {
                    this.isLoading = false;
                    // Set up periodic refresh
                    // this.timeoutId = setTimeout(this.getAllData, 5000);
                });
        },

        getEntityColor(entity) {
            const colors = {
                'sales_channel': '#3b82f6',
                'category': '#8b5cf6',
                'product': '#10b981',
                'template': '#06b6d4',
                'attribute': '#f59e0b',
                'attribute_value': '#f97316',
                'customer': '#ef4444',
                'customer_address': '#ec4899',
                'order': '#84cc16',
                'delivery': '#14b8a6',
                'transaction': '#a855f7'
            };
            return colors[entity] || '#6b7280';
        },

        getEntityDisplayName(entity) {
            const names = {
                'sales_channel': this.$tc('brainst-odoo-pro.dashboard.entityNames.salesChannels'),
                'category': this.$tc('brainst-odoo-pro.dashboard.entityNames.categories'),
                'product': this.$tc('brainst-odoo-pro.dashboard.entityNames.productVariants'),
                'template': this.$tc('brainst-odoo-pro.dashboard.entityNames.products'),
                'attribute': this.$tc('brainst-odoo-pro.dashboard.entityNames.propertyGroups'),
                'attribute_value': this.$tc('brainst-odoo-pro.dashboard.entityNames.propertyGroupOptions'),
                'customer': this.$tc('brainst-odoo-pro.dashboard.entityNames.customers'),
                'customer_address': this.$tc('brainst-odoo-pro.dashboard.entityNames.customerAddresses'),
                'order': this.$tc('brainst-odoo-pro.dashboard.entityNames.orders'),
                'delivery': this.$tc('brainst-odoo-pro.dashboard.entityNames.deliveries'),
                'transaction': this.$tc('brainst-odoo-pro.dashboard.entityNames.transactions')
            };
            return names[entity] || entity.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        },

        // Pie chart calculation methods
        getSyncedPath() {
            if (this.totalRecords === 0) return '';

            const percentage = (this.syncedCount / this.totalRecords) * 100;
            const angle = (percentage / 100) * 360;

            return this.createPieSlice(0, angle, 40);
        },

        getPendingPath() {
            if (this.totalRecords === 0) return '';

            const syncedPercentage = (this.syncedCount / this.totalRecords) * 100;
            const pendingPercentage = 100 - syncedPercentage;
            const startAngle = (syncedPercentage / 100) * 360;
            const endAngle = startAngle + (pendingPercentage / 100) * 360;

            return this.createPieSlice(startAngle, endAngle, 40);
        },

        createPieSlice(startAngle, endAngle, radius) {
            const centerX = 50;
            const centerY = 50;

            const startAngleRad = (startAngle - 90) * Math.PI / 180;
            const endAngleRad = (endAngle - 90) * Math.PI / 180;

            const x1 = centerX + radius * Math.cos(startAngleRad);
            const y1 = centerY + radius * Math.sin(startAngleRad);
            const x2 = centerX + radius * Math.cos(endAngleRad);
            const y2 = centerY + radius * Math.sin(endAngleRad);

            const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

            if (endAngle - startAngle >= 360) {
                // Full circle
                return `M ${centerX} ${centerY} m -${radius} 0 a ${radius} ${radius} 0 1 1 ${radius * 2} 0 a ${radius} ${radius} 0 1 1 -${radius * 2} 0`;
            }

            return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
        },

        // Hover methods
        showHover(type) {
            if (this.totalRecords === 0) return;

            const syncedPercentage = Math.round((this.syncedCount / this.totalRecords) * 100);
            const pendingPercentage = 100 - syncedPercentage;

            this.hoverInfo = {
                show: true,
                label: type === 'synced' ? this.$tc('brainst-odoo-pro.dashboard.synced') : this.$tc('brainst-odoo-pro.dashboard.pending'),
                percentage: type === 'synced' ? syncedPercentage : pendingPercentage
            };
        },

        hideHover() {
            this.hoverInfo.show = false;
        },

        // Get entity statistics for badges
        getEntityStats(entityKey) {
            const total = this.entityCounts[entityKey] || 0;
            const synced = this.entitySynced[entityKey] || 0;
            const pending = Math.max(0, total - synced);
            const percentage = total > 0 ? Math.round((synced / total) * 100) : 0;

            return {
                total,
                synced,
                pending,
                percentage
            };
        },

        // Get sync status for entity
        getEntitySyncStatus(entityKey) {
            const stats = this.getEntityStats(entityKey);
            if (stats.total === 0) return 'no-data';
            if (stats.synced === stats.total) return 'complete';
            if (stats.synced > 0) return 'partial';
            return 'pending';
        },

        // Get status badge variant
        getStatusBadgeVariant(status) {
            const variants = {
                'complete': 'success',
                'partial': 'warning',
                'pending': 'danger',
                'no-data': 'neutral'
            };
            return variants[status] || 'neutral';
        },

        // Get tooltip text for entity
        getEntityTooltip(entityKey) {
            const stats = this.getEntityStats(entityKey);
            const status = this.getEntitySyncStatus(entityKey);

            const statusText = {
                'complete': this.$tc('brainst-odoo-pro.dashboard.tooltips.complete'),
                'partial': this.$tc('brainst-odoo-pro.dashboard.tooltips.partial'),
                'pending': this.$tc('brainst-odoo-pro.dashboard.tooltips.pending'),
                'no-data': this.$tc('brainst-odoo-pro.dashboard.tooltips.noData')
            };

            return `${statusText[status] || ''}\n${this.$tc('brainst-odoo-pro.dashboard.synced')}: ${stats.synced}/${stats.total} (${stats.percentage}%)`;
        },

        // Get icon with fallback - using only guaranteed Shopware icons
        getEntityIcon(icon) {
            // Map to icons that definitely exist in Shopware
            const safeIcons = {
                'regular-products': 'regular-products',
                'regular-map-marker': 'regular-map-marker',
                'regular-shopping-bag': 'regular-server',
                'regular-home': 'regular-home',
                'regular-circle': 'regular-circle',
                'regular-folder': 'regular-folder',
                'regular-cog': 'regular-cog',
                'regular-tag': 'regular-tag',
                'regular-user': 'regular-user',
                'regular-shopping-cart': 'regular-shopping-cart',
                'regular-truck': 'regular-truck',
                'regular-credit-card': 'regular-credit-card',
                'regular-map': 'regular-map',
                'regular-storefront': 'regular-storefront'
            };

            return safeIcons[icon] || 'regular-server';
        },

        // Gauge chart methods
        switchTimePeriod(period) {
            this.selectedTimePeriod = period;
        },



        refreshSyncRate() {
            // Add rotation animation to the refresh button
            const refreshBtn = document.querySelector('.gauge-refresh-btn');
            if (refreshBtn) {
                // Remove any existing rotation class first
                refreshBtn.classList.remove('rotating');

                // Force reflow to ensure the class removal takes effect
                refreshBtn.offsetHeight;

                // Add the rotation class to trigger animation
                refreshBtn.classList.add('rotating');

                // Remove the rotation class after animation completes to allow for next click
                setTimeout(() => {
                    refreshBtn.classList.remove('rotating');
                }, 300);
            }

            this.brainstOdooProApiService.getSyncRate()
                .then((response) => {
                    if (response.isValid) {
                        this.syncRate = {
                            recordsPerHour: response.recordsPerHour || 0,
                            recordsPerMinute: response.recordsPerMinute || 0,
                            displayRecordsPerHour: response.displayRecordsPerHour || '0',
                            displayRecordsPerMinute: response.displayRecordsPerMinute || '0',
                            timestamp: response.timestamp
                        };
                    }
                })
                .catch((error) => {
                    console.error('Failed to fetch sync rate:', error);
                });
        }
    },
    beforeUnmount() {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
    },
});

